import { TransactionsTable } from "@/components/transactions/TransactionsTable";
import { Transaction } from "@/types";
import { render, screen } from "@testing-library/react";

describe("TransactionsTable", () => {
   const mockTransactions: Transaction[] = [
      {
         id: "1",
         amount: 43644,
         transactionId: "TR_8401857902",
         type: "Transfer",
         date: "Feb 12, 2022",
         time: "10:30AM",
         status: "Processed",
      },
      {
         id: "2",
         amount: 35471,
         transactionId: "TR_8401857903",
         type: "Withdrawal",
         date: "Feb 12, 2022",
         time: "11:30AM",
         status: "Failed",
      },
   ];

   it("renders loading state", () => {
      render(<TransactionsTable transactions={[]} isLoading={true} />);

      expect(screen.getByText("Loading transactions...")).toBeInTheDocument();
   });

   it("renders desktop table with transactions", () => {
      render(<TransactionsTable transactions={mockTransactions} />);

      // Check table headers
      expect(screen.getByText("AMOUNT")).toBeInTheDocument();
      expect(screen.getByText("TRANSACTION ID")).toBeInTheDocument();
      expect(screen.getByText("TRANSACTION TYPE")).toBeInTheDocument();
      expect(screen.getByText("DATE")).toBeInTheDocument();
      expect(screen.getByText("TIME")).toBeInTheDocument();
      expect(screen.getByText("STATUS")).toBeInTheDocument();

      // Check transaction data (using getAllByText since both desktop and mobile views render)
      expect(screen.getAllByText("₦43,644")).toHaveLength(2); // Desktop table + mobile card
      expect(screen.getAllByText("TR_8401857902")).toHaveLength(2); // Desktop table + mobile card
      expect(screen.getAllByText("Transfer")).toHaveLength(2); // Desktop table + mobile card
      expect(screen.getAllByText("Feb 12, 2022")).toHaveLength(2); // Desktop table + mobile card
      expect(screen.getAllByText("10:30AM")).toHaveLength(2); // Desktop table + mobile card
   });

   it("renders status badges correctly", () => {
      render(<TransactionsTable transactions={mockTransactions} />);

      const processedBadges = screen.getAllByText("Processed");
      const failedBadges = screen.getAllByText("Failed");

      // Should have 2 of each (desktop + mobile)
      expect(processedBadges).toHaveLength(2);
      expect(failedBadges).toHaveLength(2);

      processedBadges.forEach((badge) => {
         expect(badge).toHaveClass("fundr-status-success");
      });
      failedBadges.forEach((badge) => {
         expect(badge).toHaveClass("fundr-status-failed");
      });
   });

   it("formats currency correctly", () => {
      render(<TransactionsTable transactions={mockTransactions} />);

      expect(screen.getAllByText("₦43,644")).toHaveLength(2); // Desktop + mobile
      expect(screen.getAllByText("₦35,471")).toHaveLength(2); // Desktop + mobile
   });

   it("renders mobile cards view", () => {
      render(<TransactionsTable transactions={mockTransactions} />);

      // Mobile cards should contain transaction data (both transactions render mobile cards)
      expect(screen.getAllByText("Transaction ID:")).toHaveLength(2);
      expect(screen.getAllByText("Date:")).toHaveLength(2);
   });

   it("handles empty transactions array", () => {
      render(<TransactionsTable transactions={[]} />);

      // Should render table structure but no data rows
      expect(screen.getByText("AMOUNT")).toBeInTheDocument();
      expect(screen.queryByText("₦43,644")).not.toBeInTheDocument();
   });
});
