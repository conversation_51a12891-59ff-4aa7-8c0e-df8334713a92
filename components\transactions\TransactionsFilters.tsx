"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
   Popover,
   PopoverContent,
   PopoverTrigger,
} from "@/components/ui/popover";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { TransactionFilters } from "@/types";
import { format } from "date-fns";
import { Calendar, CalendarDays } from "lucide-react";
import { useEffect, useState } from "react";

interface TransactionsFiltersProps {
   filters: TransactionFilters;
   onFiltersChange: (filters: Partial<TransactionFilters>) => void;
}

export function TransactionsFilters({
   filters,
   onFiltersChange,
}: TransactionsFiltersProps) {
   const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
   const [isMobile, setIsMobile] = useState(false);

   useEffect(() => {
      const checkMobile = () => {
         setIsMobile(window.innerWidth < 768);
      };

      checkMobile();
      window.addEventListener("resize", checkMobile);

      return () => window.removeEventListener("resize", checkMobile);
   }, []);

   const handleDateRangeChange = (
      from: Date | undefined,
      to: Date | undefined
   ) => {
      onFiltersChange({
         dateRange: {
            from: from || null,
            to: to || null,
         },
      });
   };

   const formatDateRange = () => {
      if (filters.dateRange.from && filters.dateRange.to) {
         return `${format(filters.dateRange.from, "MMM d, yyyy")} - ${format(
            filters.dateRange.to,
            "MMM d, yyyy"
         )}`;
      }
      return "Select Date Range";
   };

   return (
      <div className="flex flex-col gap-4">
         <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <Select
               value={filters.account}
               onValueChange={(value) => onFiltersChange({ account: value })}
            >
               <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Select account" />
               </SelectTrigger>
               <SelectContent>
                  <SelectItem value="All Accounts">All Accounts</SelectItem>
                  <SelectItem value="Sterling Bank">Sterling Bank</SelectItem>
                  <SelectItem value="Other Bank">Other Bank</SelectItem>
               </SelectContent>
            </Select>

            <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
               <PopoverTrigger asChild>
                  <Button
                     variant="outline"
                     className="w-full sm:w-[280px] justify-start text-left font-normal"
                  >
                     <CalendarDays className="mr-2 h-4 w-4" />
                     {formatDateRange()}
                  </Button>
               </PopoverTrigger>
               <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                     mode="range"
                     selected={{
                        from: filters.dateRange.from || undefined,
                        to: filters.dateRange.to || undefined,
                     }}
                     onSelect={(range) => {
                        handleDateRangeChange(range?.from, range?.to);
                        if (range?.from && range?.to) {
                           setIsDatePickerOpen(false);
                        }
                     }}
                     numberOfMonths={isMobile ? 1 : 2}
                  />
               </PopoverContent>
            </Popover>

            <Button
               variant="outline"
               className="flex items-center gap-2 w-full sm:w-auto"
            >
               <Calendar className="h-4 w-4" />
               Export
            </Button>
         </div>
      </div>
   );
}
