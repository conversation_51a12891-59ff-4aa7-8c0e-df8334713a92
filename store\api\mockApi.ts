import { 
  ApiResponse, 
  DashboardData, 
  TransactionsData, 
  Transaction, 
  TransactionFilters,
  ChartData 
} from '@/types'

// Mock data
const mockChartData: ChartData[] = [
  { month: 'Jan', value: 320000 },
  { month: 'Feb', value: 450000 },
  { month: 'Mar', value: 380000 },
  { month: 'Apr', value: 280000 },
  { month: 'May', value: 150000 },
  { month: 'Jun', value: 180000 },
  { month: 'Jul', value: 160000 },
  { month: 'Aug', value: 170000 },
  { month: 'Sep', value: 160000 },
  { month: 'Oct', value: 180000 },
  { month: 'Nov', value: 220000 },
  { month: 'Dec', value: 0 },
]

const mockTransactions: Transaction[] = [
  {
    id: '1',
    amount: 43644,
    transactionId: 'TR_8401857902',
    type: 'Transfer',
    date: 'Feb 12, 2022',
    time: '10:30AM',
    status: 'Processed'
  },
  {
    id: '2',
    amount: 35471,
    transactionId: 'TR_8401857902',
    type: 'Withdrawal',
    date: 'Feb 12, 2022',
    time: '10:30AM',
    status: 'Failed'
  },
  {
    id: '3',
    amount: 43644,
    transactionId: 'TR_8401857902',
    type: 'Deposit',
    date: 'Feb 12, 2022',
    time: '10:30AM',
    status: 'Processed'
  },
  {
    id: '4',
    amount: 35471,
    transactionId: 'TR_8401857902',
    type: 'Request',
    date: 'Feb 12, 2022',
    time: '10:30AM',
    status: 'Failed'
  },
  {
    id: '5',
    amount: 43644,
    transactionId: 'TR_8401857902',
    type: 'Transfer',
    date: 'Feb 12, 2022',
    time: '10:30AM',
    status: 'Processed'
  },
  {
    id: '6',
    amount: 35471,
    transactionId: 'TR_8401857902',
    type: 'Transfer',
    date: 'Feb 12, 2022',
    time: '10:30AM',
    status: 'Failed'
  },
  {
    id: '7',
    amount: 38948,
    transactionId: 'TR_8401857902',
    type: 'Transfer',
    date: 'Feb 12, 2022',
    time: '10:30AM',
    status: 'Processed'
  },
]

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const mockApi = {
  async getDashboardData(period: string): Promise<ApiResponse<DashboardData>> {
    await delay(500) // Simulate network delay
    
    return {
      success: true,
      data: {
        metrics: {
          revenue: {
            current: 0,
            change: -100,
            period: 'vs Last 7 days'
          },
          totalValue: 0,
          accountDetails: {
            bank: 'STERLING BANK',
            accountNumber: '**********',
            businessName: 'OGEDENGBE FRUITS STORE'
          }
        },
        chartData: mockChartData
      }
    }
  },

  async getTransactions(
    page: number = 1, 
    filters: TransactionFilters
  ): Promise<ApiResponse<TransactionsData>> {
    await delay(300) // Simulate network delay
    
    const itemsPerPage = 6
    const startIndex = (page - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    
    return {
      success: true,
      data: {
        transactions: mockTransactions.slice(startIndex, endIndex),
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(mockTransactions.length / itemsPerPage),
          totalItems: mockTransactions.length,
          itemsPerPage
        }
      }
    }
  }
}
