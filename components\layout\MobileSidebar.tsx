'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { 
  LayoutDashboard, 
  CreditCard, 
  ArrowUpDown, 
  Users, 
  Settings,
  Globe
} from 'lucide-react'
import { cn } from '@/lib/utils'

const navigation = [
  { name: 'Get Started', href: '/get-started', icon: Globe },
  { name: 'Dashboard', href: '/', icon: LayoutDashboard },
  { name: 'Accounts', href: '/accounts', icon: CreditCard },
  { name: 'Transfers', href: '/transfers', icon: ArrowUpDown },
  { name: 'Transactions', href: '/transactions', icon: Users },
  { name: 'Settings', href: '/settings', icon: Settings },
]

export function MobileSidebar() {
  const pathname = usePathname()

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-1">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                isActive
                  ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              )}
            >
              <item.icon 
                className={cn(
                  'mr-3 h-5 w-5',
                  isActive ? 'text-blue-700' : 'text-gray-400'
                )} 
              />
              {item.name}
            </Link>
          )
        })}
      </nav>
    </div>
  )
}
