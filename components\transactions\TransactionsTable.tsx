"use client";

import { Badge } from "@/components/ui/badge";
import {
   Table,
   TableBody,
   TableCell,
   TableHead,
   TableHeader,
   TableRow,
} from "@/components/ui/table";
import { Transaction } from "@/types";

interface TransactionsTableProps {
   transactions: Transaction[];
   isLoading?: boolean;
}

export function TransactionsTable({
   transactions,
   isLoading,
}: TransactionsTableProps) {
   const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat("en-NG", {
         style: "currency",
         currency: "NGN",
         minimumFractionDigits: 0,
         maximumFractionDigits: 0,
      }).format(amount);
   };

   const getStatusBadge = (status: string) => {
      if (status === "Processed") {
         return <Badge className="fundr-status-success">Processed</Badge>;
      }
      return <Badge className="fundr-status-failed">Failed</Badge>;
   };

   if (isLoading) {
      return (
         <div className="flex items-center justify-center h-64">
            <div className="text-gray-500">Loading transactions...</div>
         </div>
      );
   }

   return (
      <div className="fundr-card">
         {/* Desktop Table */}
         <div className="hidden md:block">
            <Table>
               <TableHeader>
                  <TableRow>
                     <TableHead className="w-[100px]">AMOUNT</TableHead>
                     <TableHead>TRANSACTION ID</TableHead>
                     <TableHead>TRANSACTION TYPE</TableHead>
                     <TableHead>DATE</TableHead>
                     <TableHead>TIME</TableHead>
                     <TableHead>STATUS</TableHead>
                  </TableRow>
               </TableHeader>
               <TableBody>
                  {transactions.map((transaction) => (
                     <TableRow key={transaction.id}>
                        <TableCell className="font-medium">
                           {formatCurrency(transaction.amount)}
                        </TableCell>
                        <TableCell className="text-gray-600">
                           {transaction.transactionId}
                        </TableCell>
                        <TableCell className="text-gray-600">
                           {transaction.type}
                        </TableCell>
                        <TableCell className="text-gray-600">
                           {transaction.date}
                        </TableCell>
                        <TableCell className="text-gray-600">
                           {transaction.time}
                        </TableCell>
                        <TableCell>
                           {getStatusBadge(transaction.status)}
                        </TableCell>
                     </TableRow>
                  ))}
               </TableBody>
            </Table>
         </div>

         {/* Mobile Cards */}
         <div className="md:hidden space-y-4 p-4">
            {transactions.map((transaction) => (
               <div
                  key={transaction.id}
                  className="border border-gray-200 rounded-lg p-4 space-y-3"
               >
                  <div className="flex justify-between items-start">
                     <div>
                        <div className="font-medium text-lg">
                           {formatCurrency(transaction.amount)}
                        </div>
                        <div className="text-sm text-gray-500">
                           {transaction.type}
                        </div>
                     </div>
                     {getStatusBadge(transaction.status)}
                  </div>
                  <div className="space-y-1">
                     <div className="text-sm">
                        <span className="text-gray-500">Transaction ID:</span>{" "}
                        <span className="text-gray-900">
                           {transaction.transactionId}
                        </span>
                     </div>
                     <div className="text-sm">
                        <span className="text-gray-500">Date:</span>{" "}
                        <span className="text-gray-900">
                           {transaction.date}, {transaction.time}
                        </span>
                     </div>
                  </div>
               </div>
            ))}
         </div>
      </div>
   );
}
