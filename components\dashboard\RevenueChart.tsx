"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartData } from "@/types";
import { <PERSON>, <PERSON><PERSON>hart, ResponsiveContainer, XAxis, <PERSON>Axis } from "recharts";

interface RevenueChartProps {
   data: ChartData[];
   revenue: {
      current: number;
      change: number;
      period: string;
   };
   selectedPeriod: string;
   onPeriodChange: (period: "today" | "last7days" | "last30days") => void;
}

export function RevenueChart({
   data,
   revenue,
   selectedPeriod,
   onPeriodChange,
}: RevenueChartProps) {
   const formatCurrency = (value: number) => {
      return new Intl.NumberFormat("en-NG", {
         style: "currency",
         currency: "NGN",
         minimumFractionDigits: 2,
      }).format(value);
   };

   const formatYAxis = (value: number) => {
      if (value >= 1000000) {
         return `${value / 1000000}M`;
      }
      if (value >= 1000) {
         return `${value / 1000}K`;
      }
      return value.toString();
   };

   return (
      <Card className="fundr-card">
         <CardHeader className="pb-4">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
               <div>
                  <CardTitle className="text-lg font-semibold text-gray-900">
                     Revenue
                  </CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                     <span className="text-sm text-gray-500">
                        {revenue.change}%
                     </span>
                     <span className="text-sm text-gray-500">
                        {revenue.period}
                     </span>
                  </div>
               </div>
               <div className="flex flex-wrap gap-2">
                  <Button
                     variant={
                        selectedPeriod === "today" ? "default" : "outline"
                     }
                     size="sm"
                     onClick={() => onPeriodChange("today")}
                     className="text-xs"
                  >
                     Today
                  </Button>
                  <Button
                     variant={
                        selectedPeriod === "last7days" ? "default" : "outline"
                     }
                     size="sm"
                     onClick={() => onPeriodChange("last7days")}
                     className="text-xs"
                  >
                     Last 7 days
                  </Button>
                  <Button
                     variant={
                        selectedPeriod === "last30days" ? "default" : "outline"
                     }
                     size="sm"
                     onClick={() => onPeriodChange("last30days")}
                     className="text-xs"
                  >
                     Last 30 days
                  </Button>
               </div>
            </div>
         </CardHeader>
         <CardContent>
            <div className="mb-4">
               <div className="text-2xl font-bold text-gray-900">
                  {formatCurrency(revenue.current)}
               </div>
               <div className="text-sm text-gray-500">In total value</div>
            </div>

            <div className="h-48 sm:h-64">
               <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                     data={data}
                     margin={{ top: 20, right: 10, left: 10, bottom: 5 }}
                  >
                     <XAxis
                        dataKey="month"
                        axisLine={false}
                        tickLine={false}
                        tick={{ fontSize: 11, fill: "#6b7280" }}
                     />
                     <YAxis
                        axisLine={false}
                        tickLine={false}
                        tick={{ fontSize: 11, fill: "#6b7280" }}
                        tickFormatter={formatYAxis}
                        width={40}
                     />
                     <Bar
                        dataKey="value"
                        fill="#fbbf24"
                        radius={[4, 4, 0, 0]}
                     />
                  </BarChart>
               </ResponsiveContainer>
            </div>
         </CardContent>
      </Card>
   );
}
