"use client";

import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Bell, Menu } from "lucide-react";
import Image from "next/image";
import { MobileSidebar } from "./MobileSidebar";

export function Header() {
   return (
      <header className="bg-white border-b border-gray-200 px-4 lg:px-8 h-20 flex items-center justify-between fixed top-0 left-0 right-0 z-50">
         {/* Left side - Logo and mobile menu */}
         <div className="flex items-center space-x-4">
            {/* Mobile menu button */}
            <Sheet>
               <SheetTrigger asChild>
                  <Button variant="ghost" size="icon" className="lg:hidden">
                     <Menu className="h-6 w-6" />
                     <span className="sr-only">Toggle navigation menu</span>
                  </Button>
               </SheetTrigger>
               <SheetContent side="left" className="p-0 w-64">
                  <MobileSidebar />
               </SheetContent>
            </Sheet>

            {/* Logo */}
            <div className="flex items-center">
               <Image
                  src="/images/logo.svg"
                  alt="FundR"
                  width={100}
                  height={24}
                  className="h-6 w-auto"
               />
            </div>
         </div>

         {/* Right side - notifications and user avatar */}
         <div className="flex items-center space-x-4">
            <Button
               variant="ghost"
               size="icon"
               className="relative"
               aria-label="View notifications"
            >
               <Bell className="size-6 text-gray-600" />
               <span className="sr-only">Notifications</span>
            </Button>

            <Avatar
               className="size-12"
               role="button"
               tabIndex={0}
               aria-label="User menu"
            >
               <AvatarFallback className="bg-green-500 text-white text-sm font-medium">
                  GA
               </AvatarFallback>
            </Avatar>
         </div>
      </header>
   );
}
